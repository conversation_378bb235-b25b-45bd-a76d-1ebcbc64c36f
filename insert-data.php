<?php
// 1. Database connection settings
$servername = "mydb.ics.purdue.edu";
$username = "tdoshi";
$password = "Tiad1925";
$dbname = "tdoshi";

// 2. Connect to the database
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// 3. Path to your CSV file
$csvFile = 'lab1.csv';

// Check if CSV file exists
if (!file_exists($csvFile)) {
    die("CSV file '$csvFile' not found in current directory: " . getcwd() . "<br>");
}
echo "CSV file found: $csvFile<br>";

// Arrays to track unique customers and products
$customers = [];
$products = [];

// 4. Open the CSV file
if (($handle = fopen($csvFile, "r")) !== FALSE) {
    echo "CSV file opened successfully<br>";
    $header = fgetcsv($handle); // Skip header row
    echo "Header: " . implode(", ", $header) . "<br>";

    // 5. Loop through each row in the CSV to collect unique customers and products
    $rowCount = 0;
    while (($data = fgetcsv($handle, 10000, ",")) !== FALSE) {
        $rowCount++;
        echo "Processing row $rowCount<br>";

        // Check if we have enough columns
        if (count($data) < 9) {
            echo "Skipping row $rowCount - insufficient columns<br>";
            continue;
        }
        // Map CSV columns: OrderID,OrderDate,CustomerName,CustomerEmail,ProductName,Category,Quantity,Price,Discount
        $orderID = $data[0];
        $orderDate = $data[1];
        $customerName = $data[2];
        $customerEmail = $data[3];
        $productName = $data[4];
        $category = $data[5];
        $quantity = $data[6];
        $price = $data[7];
        $discount = $data[8];

        // Store unique customers
        if (!isset($customers[$customerEmail])) {
            $customers[$customerEmail] = $customerName;
            echo "Found customer: $customerName ($customerEmail)<br>";
        }

        // Store unique products
        $productKey = $productName . '|' . $category;
        if (!isset($products[$productKey])) {
            $products[$productKey] = ['name' => $productName, 'category' => $category, 'price' => $price];
            echo "Found product: $productName ($category)<br>";
        }
    }
    fclose($handle);

    echo "Found " . count($customers) . " unique customers<br>";
    echo "Found " . count($products) . " unique products<br>";

    // Check if tables exist and show structure
    $result = $conn->query("SHOW TABLES LIKE 'Customers'");
    if ($result->num_rows == 0) {
        die("Error: Customers table does not exist. Please run create-table.php first.<br>");
    }
    echo "Customers table exists<br>";

    // Show table structure
    $result = $conn->query("DESCRIBE Customers");
    echo "Customers table structure:<br>";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }

    // Insert customers
    echo "<h3>Inserting Customers:</h3>";
    $customerIDs = [];
    $customerCounter = 1;
    foreach ($customers as $email => $name) {
        echo "Preparing to insert customer: $name ($email)<br>";
        $stmt = $conn->prepare("INSERT IGNORE INTO Customers (customer_id, customer_name, email) VALUES (?, ?, ?)");
        if (!$stmt) {
            echo "Prepare failed: " . $conn->error . "<br>";
            continue;
        }

        $stmt->bind_param("iss", $customerCounter, $name, $email);
        echo "Executing insert for customer ID: $customerCounter<br>";

        if ($stmt->execute()) {
            $customerIDs[$email] = $customerCounter;
            echo "Successfully inserted customer: $name ($email)<br>";
            $customerCounter++;
        } else {
            echo "Error inserting customer $name: " . $stmt->error . "<br>";
        }
        $stmt->close();

        // Add a small delay to prevent overwhelming the database
        usleep(100000); // 0.1 second delay
    }

    // Insert products
    echo "<h3>Inserting Products:</h3>";

    // Show Products table structure
    $result = $conn->query("DESCRIBE Products");
    echo "Products table structure:<br>";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }

    $productIDs = [];
    $productCounter = 1;
    foreach ($products as $key => $product) {
        echo "Preparing to insert product: " . $product['name'] . "<br>";
        $stmt = $conn->prepare("INSERT IGNORE INTO Products (ProductID, Name, Category, UnitPrice) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("issd", $productCounter, $product['name'], $product['category'], $product['price']);
        if ($stmt->execute()) {
            $productIDs[$key] = $productCounter;
            echo "Inserted product: " . $product['name'] . " (" . $product['category'] . ")<br>";
            $productCounter++;
        } else {
            echo "Error inserting product " . $product['name'] . ": " . $stmt->error . "<br>";
        }
        $stmt->close();
    }

    // Now process the CSV again for orders and order details
    echo "<h3>Inserting Orders and Order Details:</h3>";
    $handle = fopen($csvFile, "r");
    fgetcsv($handle); // Skip header row again

    $processedOrders = [];

    while (($data = fgetcsv($handle, 10000, ",")) !== FALSE) {
        $orderID = $data[0];
        $orderDate = DateTime::createFromFormat('n/j/Y', $data[1])->format('Y-m-d');
        $customerEmail = $data[3];
        $productName = $data[4];
        $category = $data[5];
        $quantity = $data[6];
        $price = $data[7];
        $discount = $data[8];

        // Insert order if not already processed
        if (!in_array($orderID, $processedOrders)) {
            $customerID = $customerIDs[$customerEmail];
            $stmt = $conn->prepare("INSERT IGNORE INTO Orders (OrderID, OrderDate, CustomerID) VALUES (?, ?, ?)");
            $stmt->bind_param("isi", $orderID, $orderDate, $customerID);
            if ($stmt->execute()) {
                echo "Inserted order: $orderID<br>";
                $processedOrders[] = $orderID;
            } else {
                echo "Error inserting order $orderID: " . $stmt->error . "<br>";
            }
            $stmt->close();
        }

        // Insert order detail
        $productKey = $productName . '|' . $category;
        $productID = $productIDs[$productKey];
        $stmt = $conn->prepare("INSERT INTO Order_Details (OrderID, ProductID, Quantity, UnitPrice, Discount) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("iiidd", $orderID, $productID, $quantity, $price, $discount);
        if ($stmt->execute()) {
            echo "Inserted order detail: Order $orderID, Product $productName<br>";
        } else {
            echo "Error inserting order detail: " . $stmt->error . "<br>";
        }
        $stmt->close();
    }

    fclose($handle);
    echo "<h3>Data import complete!</h3>";
} else {
    echo "Could not open CSV file.<br>";
}

$conn->close();
?>