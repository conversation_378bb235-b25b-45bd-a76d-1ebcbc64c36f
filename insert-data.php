<?php
// 1. Database connection settings
$servername = "mydb.ics.purdue.edu";
$username = "tdoshi";
$password = "Tiad1925";
$dbname = "tdoshi";

// 2. Connect to the database
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// 3. Path to your CSV file
$csvFile = 'lab1.csv';

// Arrays to track unique customers and products
$customers = [];
$products = [];

// 4. Open the CSV file
if (($handle = fopen($csvFile, "r")) !== FALSE) {
    fgetcsv($handle); // Skip header row

    // 5. Loop through each row in the CSV to collect unique customers and products
    while (($data = fgetcsv($handle, 10000, ",")) !== FALSE) {
        // Map CSV columns: OrderID,OrderDate,CustomerName,CustomerEmail,ProductName,Category,Quantity,Price,Discount
        $orderID = $data[0];
        $orderDate = $data[1];
        $customerName = $data[2];
        $customerEmail = $data[3];
        $productName = $data[4];
        $category = $data[5];
        $quantity = $data[6];
        $price = $data[7];
        $discount = $data[8];

        // Store unique customers
        if (!isset($customers[$customerEmail])) {
            $customers[$customerEmail] = $customerName;
        }

        // Store unique products
        $productKey = $productName . '|' . $category;
        if (!isset($products[$productKey])) {
            $products[$productKey] = ['name' => $productName, 'category' => $category, 'price' => $price];
        }
    }
    fclose($handle);

    // Insert customers
    echo "<h3>Inserting Customers:</h3>";
    $customerIDs = [];
    $customerCounter = 1;
    foreach ($customers as $email => $name) {
        $stmt = $conn->prepare("INSERT IGNORE INTO Customers (CustomerID, Name, Email) VALUES (?, ?, ?)");
        $stmt->bind_param("iss", $customerCounter, $name, $email);
        if ($stmt->execute()) {
            $customerIDs[$email] = $customerCounter;
            echo "Inserted customer: $name ($email)<br>";
            $customerCounter++;
        } else {
            echo "Error inserting customer $name: " . $stmt->error . "<br>";
        }
        $stmt->close();
    }

    // Insert products
    echo "<h3>Inserting Products:</h3>";
    $productIDs = [];
    $productCounter = 1;
    foreach ($products as $key => $product) {
        $stmt = $conn->prepare("INSERT IGNORE INTO Products (ProductID, Name, Category, UnitPrice) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("issd", $productCounter, $product['name'], $product['category'], $product['price']);
        if ($stmt->execute()) {
            $productIDs[$key] = $productCounter;
            echo "Inserted product: " . $product['name'] . " (" . $product['category'] . ")<br>";
            $productCounter++;
        } else {
            echo "Error inserting product " . $product['name'] . ": " . $stmt->error . "<br>";
        }
        $stmt->close();
    }

    // Now process the CSV again for orders and order details
    echo "<h3>Inserting Orders and Order Details:</h3>";
    $handle = fopen($csvFile, "r");
    fgetcsv($handle); // Skip header row again

    $processedOrders = [];

    while (($data = fgetcsv($handle, 10000, ",")) !== FALSE) {
        $orderID = $data[0];
        $orderDate = DateTime::createFromFormat('n/j/Y', $data[1])->format('Y-m-d');
        $customerEmail = $data[3];
        $productName = $data[4];
        $category = $data[5];
        $quantity = $data[6];
        $price = $data[7];
        $discount = $data[8];

        // Insert order if not already processed
        if (!in_array($orderID, $processedOrders)) {
            $customerID = $customerIDs[$customerEmail];
            $stmt = $conn->prepare("INSERT IGNORE INTO Orders (OrderID, OrderDate, CustomerID) VALUES (?, ?, ?)");
            $stmt->bind_param("isi", $orderID, $orderDate, $customerID);
            if ($stmt->execute()) {
                echo "Inserted order: $orderID<br>";
                $processedOrders[] = $orderID;
            } else {
                echo "Error inserting order $orderID: " . $stmt->error . "<br>";
            }
            $stmt->close();
        }

        // Insert order detail
        $productKey = $productName . '|' . $category;
        $productID = $productIDs[$productKey];
        $stmt = $conn->prepare("INSERT INTO Order_Details (OrderID, ProductID, Quantity, UnitPrice, Discount) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("iiidd", $orderID, $productID, $quantity, $price, $discount);
        if ($stmt->execute()) {
            echo "Inserted order detail: Order $orderID, Product $productName<br>";
        } else {
            echo "Error inserting order detail: " . $stmt->error . "<br>";
        }
        $stmt->close();
    }

    fclose($handle);
    echo "<h3>Data import complete!</h3>";
} else {
    echo "Could not open CSV file.<br>";
}

$conn->close();
?>