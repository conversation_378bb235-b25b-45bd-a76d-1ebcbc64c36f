<?php
// 1. Database connection settings
$servername = "mydb.ics.purdue.edu";
$username = "tdoshi";
$password = "Tiad1925";
$dbname = "tdoshi";

// 2. Connect to the database
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// 3. Path to your CSV file
$csvFile = 'lab1.csv';

// 4. Open the CSV file
if (($handle = fopen($csvFile, "r")) !== FALSE) {
    fgetcsv($handle); // Skip header row

    // 5. Loop through each row in the CSV
    while (($data = fgetcsv($handle, 10000, ",")) !== FALSE) {
        // Example: Map CSV columns to variables
        $col1 = $data[0]; // Change to your column name
        $col2 = $data[1]; // Change to your column name
        // Add more columns as needed

        // 6. Prepare and execute the INSERT statement
        // Change table_name and columns to match your model
        $stmt = $conn->prepare("INSERT INTO table_name (column1, column2) VALUES (?, ?)");
        $stmt->bind_param("ss", $col1, $col2); // Change types as needed
        if ($stmt->execute()) {
            echo "Inserted: $col1, $col2<br>";
        } else {
            echo "Error: " . $stmt->error . "<br>";
        }
        $stmt->close();
    }
    fclose($handle);
    echo "Data import complete.<br>";
} else {
    echo "Could not open CSV file.<br>";
}

$conn->close();
?>