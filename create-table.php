<?php
$servername = "mydb.ics.purdue.edu";
$username = "tdoshi";
$password = "Tiad1925";
$dbname = "tdoshi";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// SQL query to create a table
$sql = "CREATE TABLE IF NOT EXISTS Orders (
    order_id INT PRIMARY KEY,
    order_date DATE NOT NULL,
    customer_id INT NOT NULL
)";

// Execute the query
if ($conn->query($sql) === TRUE) {
    echo "Table Orders created successfully";
} else {
    echo "<br>Error creating table: " . $conn->error;
}

$conn->close();
?>