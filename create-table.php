<?php
$servername = "mydb.ics.purdue.edu";
$username = "tdoshi";
$password = "Tiad1925";
$dbname = "tdoshi";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// SQL queries to create tables

// Create Customers table
$sql_customers = "CREATE TABLE IF NOT EXISTS Customers (
    CustomerID INT PRIMARY KEY AUTO_INCREMENT,
    Name VARCHAR(100) NOT NULL,
    Email VARCHAR(255) NOT NULL UNIQUE
)";

// Create Products table
$sql_products = "CREATE TABLE IF NOT EXISTS Products (
    ProductID INT PRIMARY KEY AUTO_INCREMENT,
    Name VARCHAR(100) NOT NULL,
    Category VARCHAR(50) NOT NULL,
    UnitPrice DECIMAL(10,2) NOT NULL
)";

// Create Orders table
$sql_orders = "CREATE TABLE IF NOT EXISTS Orders (
    OrderID INT PRIMARY KEY,
    OrderDate DATE NOT NULL,
    CustomerID INT NOT NULL,
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
)";

// Create Order_Details table
$sql_order_details = "CREATE TABLE IF NOT EXISTS Order_Details (
    OrderID INT NOT NULL,
    ProductID INT NOT NULL,
    Quantity INT NOT NULL,
    UnitPrice DECIMAL(10,2) NOT NULL,
    Discount DECIMAL(4,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY (OrderID, ProductID),
    FOREIGN KEY (OrderID) REFERENCES Orders(OrderID),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
)";

// Array of SQL statements
$tables = [
    'Customers' => $sql_customers,
    'Products' => $sql_products,
    'Orders' => $sql_orders,
    'Order_Details' => $sql_order_details
];

// Execute each query
foreach ($tables as $table_name => $sql) {
    if ($conn->query($sql) === TRUE) {
        echo "Table $table_name created successfully<br>";
    } else {
        echo "Error creating table $table_name: " . $conn->error . "<br>";
    }
}

$conn->close();
?>